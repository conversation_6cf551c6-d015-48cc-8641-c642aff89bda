{layout name="layout1" /}
<style>
    .layui-table-cell {
        height:auto;
    }
    .goods-content>div:not(:last-of-type) {
        bcommission-bottom:1px solid #DCDCDC;
    }
    .goods-data::after{
        display: block;
        content: '';
        clear: both;
    }
    .goods_name_hide{
        overflow:hidden;
        white-space:nowrap;
        text-overflow: ellipsis;
    }
    .operation-btn {
        margin: 5px;
    }
    .table-operate{
        text-align: left;
        font-size:14px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>

<div class="wrapper">
    <div class="layui-card">
        <div class="layui-card-body">
        <div class="layui-collapse like-layui-collapse" lay-accordion="" style="bcommission:1px dashed #c4c4c4">
            <div class="layui-colla-item">
                <h2 class="layui-colla-title like-layui-colla-title" style="background-color: #fff">操作提示</h2>
                <div class="layui-colla-content layui-show">
                    <p>*审核会员的佣金提现申请。</p>
                    <p>*佣金提现支持微信、支付宝转账。提现失败会退回全部金额。</p>
                </div>
            </div>
        </div>
        <!-- 提现汇总-->
        <div class="layui-form-item div-flex">
            <fieldset class="layui-elem-field layui-field-title">
                <legend>提现汇总</legend>
            </fieldset>
        </div>


        <div class="layui-row layui-col-space15">
            <div class="layui-col-sm6 layui-col-md3" >
                <div class="layui-card" >
                    <div class="layui-card-header" >
                        已提现佣金金额
                    </div>
                    <div class="layui-card-body layuiadmin-card-list" >
                        <p class="layuiadmin-big-font">￥{$summary.successWithdraw}</p>
                    </div>
                </div>
            </div>

            <div class="layui-col-sm6 layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">
                        提现中佣金金额
                    </div>
                    <div class="layui-card-body layuiadmin-card-list">
                        <p class="layuiadmin-big-font">￥{$summary.handleWithdraw}</p>
                    </div>
                </div>
            </div>

            <div class="layui-col-sm6 layui-col-md3">
                <div class="layui-card">
                    <div class="layui-card-header">
                        可提现佣金金额
                    </div>
                    <div class="layui-card-body layuiadmin-card-list">
                        <p class="layuiadmin-big-font" >￥{$summary.totalEarnings}</p>
                    </div>
                </div>
            </div>

        </div>
        </div>
        <div class="layui-tab layui-tab-card" lay-filter="tab-all">



            <div class="layui-card-body layui-form">
                <div class="layui-form-item">
                    <div class="layui-row">
                        <div class="layui-inline">
                            <label class="layui-form-label">会员信息:</label>
                            <div class="layui-input-block">
                                <select name="search_key">
                                    <option value="user_sn">会员编号</option>
                                    <option value="nickname">会员昵称</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <input type="text" name="keyword" id="keyword" placeholder="请输入搜索内容"
                                   autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">提现单号:</label>
                            <div class="layui-input-block">
                                <input type="text" name="withdraw_sn" id="withdraw_sn" placeholder="请输入提现单号"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>

                        <div class="layui-inline">
                            <label class="layui-form-label">提现方式:</label>
                            <div class="layui-input-block">
                                <select name="type" id="type">
                                    <option value="">全部</option>
                                    <option value="1">钱包余额</option>
                                    <option value="2">微信零钱</option>
                                    <option value="3">微信收款码</option>
                                    <option value="4">支付宝收款码</option>
                                    <option value="5">银行卡</option>
                                </select>
                            </div>
                        </div>


                        <div class="layui-inline">
                            <label class="layui-form-label">提现时间:</label>
                            <div class="layui-input-inline">
                                <div class="layui-input-inline">
                                    <input type="text" name="start_time" class="layui-input" id="start_time"
                                           placeholder="" autocomplete="off">
                                </div>
                            </div>
                            <div class="layui-input-inline" style="margin-right: 5px;width: 20px;">
                                <label class="layui-form-mid">至</label>
                            </div>
                            <div class="layui-input-inline">
                                <input type="text" name="end_time" class="layui-input" id="end_time"
                                       placeholder="" autocomplete="off">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-btn-group">
                                <button type="button" id="today" day="1" class="layui-btn layui-btn-sm layui-btn-normal day">今天</button>
                                <button type="button"  day="-1" class="layui-btn layui-btn-sm layui-btn-primary day">昨天</button>
                                <button type="button"  day="7" class="layui-btn layui-btn-sm layui-btn-primary day">近7天</button>
                                <button type="button"  day="30" class="layui-btn layui-btn-sm layui-btn-primary day">近30天</button>
                            </div>
                        </div>

                        <div class="layui-inline">
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad {$view_theme_color}" lay-submit
                                    lay-filter="commission-search">查询
                            </button>
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit
                                    lay-filter="commission-clear-search">重置
                            </button>
                            <button class="layui-btn layui-btn-sm layuiadmin-btn-ad layui-btn-primary " lay-submit
                                    lay-filter="data-export">导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-tab-item layui-show">
                <div class="layui-card">
                    <ul class="layui-tab-title">
                        <li data-status='' class="layui-this">全部</li>
                        <li data-status='1'>待提现</li>
                        <li data-status="2">提现中</li>
                        <li data-status="3">提现成功</li>
                        <li data-status="4">提现失败</li>
                    </ul>
                    <div class="layui-card-body">
                        <table id="commission-lists" lay-filter="commission-lists"></table>


                        <!--会员信息-->
                        <script type="text/html" id="user">
                            <img src="{{d.user.avatar}}" style="height:60px;width: 60px" class="image-show">
                            <div class="layui-input-inline"  style="text-align: left;">
                                <p>会员编号:{{d.user.sn}}</p>
                                <p>会员昵称:{{d.user.nickname}}</p>
                                <p>用户等级:{{d.user.user_level_name}}</p>
                            </div>
                        </script>

                        <!--操作-->
                        <script type="text/html" id="withdraw-operation">
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="detail">详情</a>
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="withdraw_order_list">对账单</a>
                            <!-- 待提现的才能审核 -->
                            {{#  if(d.status == 1){ }}
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="review">审核</a>
                            {{#  } }}
                            <!-- 提现中的微信零钱申请单才能查询结果 -->
                            {{#  if(d.status == 2 && d.type == 2){ }}
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="search">结果</a>
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="withdraw_failed">失败</a>
                            {{#  } }}
                            <!-- 提现中的收款码申请单才能转账操作 -->
                            {{#  if(d.status == 2 && (d.type == 3 || d.type == 4 || d.type == 5)){ }}
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="transfer">转账</a>
                            {{#  } }}
                            {{#  if(d.status == 2 && (d.type == 6)){ }}
                            <a class="layui-btn layui-btn-primary layui-btn-sm" lay-event="send_huifu">提交汇付提现</a>
                            {{#  } }}
                        </script>


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
      layui.config({
        version:"{$front_version}",
        base: '/static/lib/' //静态资源所在路径
    }).use(['form'], function(){
        var $ = layui.$
            , form = layui.form
            , table = layui.table
            , element = layui.element
            , laydate = layui.laydate;

        //图片放大
        $(document).on('click', '.image-show', function () {
            var src = $(this).attr('src');
            like.showImg(src,400);
        });

        //监听搜索
        form.on('submit(commission-search)', function (data) {
            var field = data.field;
            //执行重载
            table.reload('commission-lists', {
                where: field,
                page: {
                    curr: 1
                }
            });
        });
        //清空查询
        form.on('submit(commission-clear-search)', function () {
            $('#withdraw_sn').val('');
            $('#type').val('');
            $('#keyword').val('');
            $('#start_time').val('{$today[0]}');
            $('#end_time').val('{$today[1]}');
            $('#today').trigger("click");
            form.render('select');
            //刷新列表
            table.reload('commission-lists', {
                where: [],
                page: {
                    curr: 1
                }
            });
        });

          // 导出
          form.on('submit(data-export)', function (data) {
              var field = data.field;
              field.status = $(".layui-tab-title li.layui-this").attr("data-status")
              like.ajax({
                  url: '{:url("finance.User/withdrawalExport")}'
                  , data: field
                  , type: 'get'
                  , success: function (res) {
                      if (res.code == 1) {
                          window.location.href = res.data.url;
                      }
                  }
              });
          });

        //日期时间范围
        laydate.render({
            elem: '#start_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
            , value: "{$today[0]}"
        });

        laydate.render({
            elem: '#end_time'
            , type: 'datetime'
            ,theme: '#1E9FFF'
            ,value: "{$today[1]}"
        });

        //获取列表
        getList('');
        //切换列表
        element.on('tab(tab-all)', function (data) {
            $('#keyword').val('');
            $('#commission_status').val('');
            $('#goods_name').val('');
            $('#pay_way').val('');
            $('#pay_status').val('');
            $('#commission_type').val('');
            $('#start_time').val('{$today[0]}');
            $('#end_time').val('{$today[1]}');
            $('#today').trigger("click");
            $('#delivery_type').val('');
            form.render('select');
            var type = $(this).attr('data-status');
            getList(type);
            if (type !== ''){
                $('.commission_status').hide();
            }else {
                $('.commission_status').show();
            }
        });

        function getList(type) {
            table.render({
                elem: '#commission-lists'
                , url: '{:url("finance.User/withdrawal")}?status=' + type
                , cols: [[
                      {field: 'sn', title: '提现单号', align: 'center',width:180}
                    , {field: 'user', title: '会员信息', align: 'center',templet:'#user',width:280}
                    , {field: 'mobile', title: '手机号码', align: 'center',width:160}
                    , {field: 'left_money', title: '提现金额', align: 'center',width:100}
                    , {field: 'type_text', title: '提现方式', align: 'center',width:100}
                    , {field: 'status_text', title: '提现状态', align: 'center',width:100}
                    , {field: 'remark', title: '提现说明', align: 'center',width:100}
                    , {field: 'create_time', title: '提现时间', align: 'center',width:200}
                    ,{fixed: 'right' ,title: '操作', align: 'center',width:360, toolbar: '#withdraw-operation'}
                ]]
                , page: true
                , text: {none: '暂无数据！'}
                ,response: {
                    statusCode: 1
                  }
                , parseData: function (res) {
                    return {
                        "code": res.code,
                        "msg": res.msg,
                        "count": res.data.count,
                        "data": res.data.lists,
                    };
                }
                ,done: function(res, curr, count){
                    // 解决操作栏因为内容过多换行问题
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                }
            });
        }

        //监听工具条
        table.on('tool(commission-lists)', function (obj) {
            var id = obj.data.id;

            // 详情
            if(obj.event === 'detail') {
                layer.open({
                    type: 2
                    , title: '提现详情'
                    , content: '{:url("finance.User/withdraw_detail")}?id=' + id
                    , area: ['90%', '90%']
                });
            }
            // 详情
            if(obj.event === 'withdraw_order_list') {
                layer.open({
                    type: 2
                    , title: '提现账单列表'
                    , content: '{:url("finance.User/withdraw_order_list")}?id=' + id
                    , area: ['90%', '90%']
                });
            }
            // 审核
            if(obj.event === 'review') {
                layer.open({
                    type: 2
                    , title: '提现审核'
                    , content: '{:url("finance.User/withdraw_review")}?id=' + id
                    , area: ['60%', '60%']
                });
            }

            // 查询结果
            if(obj.event === 'search') {
                like.ajax({
                    url:'{:url("finance.User/search")}',
                    data:{'id':id},
                    type:"post",
                    success:function(res)
                    {
                        if(res.code == 1)
                        {
                            layui.layer.msg(res.msg, {offset: '15px', icon: 1, time: 1000}, function(){
                                location.reload();
                            });
                        }
                    }
                });
            }

            // 提现失败
            if(obj.event === 'withdraw_failed') {
                layer.confirm('提现失败将退回佣金,确定要操作吗?', {icon: 3, title:'提示'}, function(index){
                    like.ajax({
                        url:'{:url("finance.User/withdrawFailed")}',
                        data:{'id':id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg('提现失败退回佣金', {offset: '15px', icon: 1, time: 1000},function(){
                                    // 关闭对话框
                                    layer.close(index);
                                    location.reload();
                                });
                            }
                        }
                    });

                });
            }
            if(obj.event === 'send_huifu') {
                layer.confirm('提交汇付后会转账到银行卡 确定要操作吗?', {icon: 3, title:'提示'}, function(index){
                    like.ajax({
                        url:'{:url("finance.User/sendHuifu")}',
                        data:{'id':id},
                        type:"post",
                        success:function(res)
                        {
                            if(res.code == 1)
                            {
                                layui.layer.msg('提现失败退回佣金', {offset: '15px', icon: 1, time: 1000},function(){
                                    // 关闭对话框
                                    layer.close(index);
                                    location.reload();
                                });
                            }
                        }
                    });

                });
            }

            // 转账
            if(obj.event === 'transfer') {
                layer.open({
                    type: 2
                    , title: '转账'
                    , content: '{:url("finance.User/transfer")}?id=' + id
                    , area: ['90%', '90%']
                });
            }
        });


        $('.day').click(function(){
            $('.day').removeClass('layui-btn-normal');
            $('.day').removeClass('layui-btn-primary');
            $('.day').addClass('layui-btn-primary');
            $(this).removeClass('layui-btn-primary');
            $(this).addClass('layui-btn-normal');
            var day = $(this).attr('day');
            switch (day) {
                case '-1':
                    $('#start_time').val('{$yesterday[0]}');
                    $('#end_time').val('{$yesterday[1]}');
                    break;
                case '1':
                    $('#start_time').val('{$today[0]}');
                    $('#end_time').val('{$today[1]}');
                    break;
                case '7':
                    $('#start_time').val('{$days_ago7[0]}');
                    $('#end_time').val('{$days_ago7[1]}');
                    break;
                case '30':
                    $('#start_time').val('{$days_ago30[0]}');
                    $('#end_time').val('{$days_ago30[1]}');
                    break;
            }
        });
    });
</script>
