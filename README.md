# JoyLoop 电商系统

## 项目概述
JoyLoop 是一个基于 ThinkPHP 6.0 框架开发的全功能电商系统，支持多端访问（PC、移动端、管理后台）和丰富的电商功能。

## 主要功能模块
- **后台管理模块**：位于 `app/admin`，提供商品、订单、用户等管理功能
- **API接口模块**：位于 `app/api` 和 `app/shopapi`，为前端提供数据接口
- **店铺模块**：位于 `app/shop`，处理店铺相关业务逻辑
- **客服系统**：位于 `app/kefuapi`，提供在线客服功能
- **前端展示**：位于 `public/pc` 和 `public/mobile`，分别对应PC端和移动端

## 技术栈
- **核心框架**：ThinkPHP 6.0
- **数据库**：MySQL (表前缀: ls_)
- **支付集成**：微信支付、支付宝支付
- **云存储**：支持阿里云OSS、腾讯云COS、七牛云
- **其他组件**：
  - 微信开发SDK (overtrue/wechat)
  - Excel处理 (phpspreadsheet)
  - 邮件发送 (phpmailer)
  - 二维码生成 (endroid/qr-code)
  - Swoole支持

## 安装指南
1. 确保环境满足以下要求：
   - PHP >= 7.4
   - MySQL >= 5.7
   - Composer

2. 克隆项目：
   ```bash
   git clone [项目仓库地址]
   ```

3. 安装依赖：
   ```bash
   composer install
   ```

4. 配置环境：
   复制 `.example.env` 为 `.env` 并修改数据库配置：
   ```
   DATABASE_TYPE=mysql
   DATABASE_HOSTNAME=localhost
   DATABASE_DATABASE=your_database_name
   DATABASE_USERNAME=your_username
   DATABASE_PASSWORD=your_password
   DATABASE_PREFIX=ls_
   ```

5. 导入数据库：
   ```bash
   mysql -u username -p database_name < dev2_ytzzmall_c.sql
   ```

6. 设置目录权限：
   ```bash
   chmod -R 755 runtime
   chmod -R 755 public/uploads
   ```

## 目录结构说明
```
joyloop/
├── app/                    # 应用代码
│   ├── admin/              # 后台管理模块
│   ├── api/                # API接口模块
│   ├── common/             # 公共代码
│   ├── shop/               # 店铺模块
│   └── ...                 # 其他模块
├── config/                 # 配置文件
│   ├── app.php             # 应用配置
│   ├── database.php        # 数据库配置
│   └── ...                 # 其他配置
├── public/                 # 公开目录
│   ├── pc/                 # PC端前端
│   ├── mobile/             # 移动端前端
│   └── ...                 # 静态资源
├── runtime/                # 运行时文件
└── ...                     # 其他目录
```

## 开发指南
1. 开发新API：
   - 在 `app/api/controller` 下创建控制器
   - 在 `app/api/logic` 下编写业务逻辑
   - 在 `app/api/validate` 下添加验证器

2. 添加新配置：
   - 在 `config` 目录下创建新的配置文件
   - 通过 `env()` 函数读取环境变量

3. 数据库迁移：
   - 使用ThinkPHP的迁移工具管理数据库变更

## 贡献指南
欢迎提交Pull Request，请确保：
- 代码符合PSR-2规范
- 添加必要的单元测试
- 更新相关文档

## 许可证
[MIT](LICENSE.txt)