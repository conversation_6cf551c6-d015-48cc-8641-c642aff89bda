<?php
namespace app\api\controller;

use app\common\basics\Api;
use app\api\logic\UserLogic;
use app\api\logic\UserHuifuLogic;
use app\api\validate\UserHuifuValidate;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\user\UserAuthentication;
use app\common\server\FileServer;
use app\common\server\HuifuPayServer;
use app\common\server\JsonServer;
use app\common\model\distribution\Distribution;
use think\exception\ValidateException;
use think\facade\Db;

class UserHuifu extends Api
{
    /**
     * 行业列表
     */
    public function hangye()
    {
        try {
            // 使用Logic类处理业务逻辑
            $result = UserHuifuLogic::getHangyeData();

            if ($result) {
                return JsonServer::success('获取行业列表成功', $result);
            } else {
                return JsonServer::error('行业列表文件不存在');
            }
        } catch (\Exception $e) {
            return JsonServer::error('获取行业列表失败：' . $e->getMessage());
        }
    }

    /**
     * 上传文件到汇付
     */
    public function uploadHuifu()
    {
        try {
            $post = $this->request->post();

            // 使用Logic类处理业务逻辑
            $result = UserHuifuLogic::uploadHuifuFile($this->user_id, $post);

            if ($result) {
                return JsonServer::success('上传成功', $result);
            } else {
                // 检查是否有错误信息
                $huifuServer = HuifuPayServer::getInstance();
                $error = $huifuServer->getError();
                return JsonServer::error($error ?: '未获取文件内容类型或上传失败');
            }
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        } catch (\Exception $e) {
            return JsonServer::error('上传失败：' . $e->getMessage());
        }
    }

    /**
     * 保存用户认证信息
     */
    public function dataSave()
    {
        try {
            $post = $this->request->post();

            // 注意：数据验证已暂时关闭，后续需自行开启
            // 使用Logic类处理业务逻辑
            $result = UserHuifuLogic::saveAuthInfo($this->user_id, $post);
            if ($result) {
                return JsonServer::success('提交认证信息成功');
            }
            return JsonServer::error('提交认证信息失败');
        } catch (ValidateException $e) {
            return JsonServer::error($e->getMessage());
        } catch (\Exception $e) {
            return JsonServer::error('操作失败：' . $e->getMessage());
        }
    }

    /**
     * 只更新用户认证信息
     */
    public function dataSave2()
    {
        try {
            $post = $this->request->post();

            // 调用逻辑层处理保存和汇付接口调用
            $result = UserHuifuLogic::saveAndCallHuifu($this->user_id, $post);

            if ($result['status']) {
                return JsonServer::success($result['message']);
            } else {
                return JsonServer::error($result['message']);
            }
        } catch (\Exception $e) {
            return JsonServer::error('操作失败：' . $e->getMessage());
        }
    }

    /**
     * 根据用户ID查询认证信息
     */
    public function getInfo()
    {
        try {
            $user_reg_type = $this->request->get('user_reg_type', 1);
            // 使用Logic类处理业务逻辑
            $info = UserHuifuLogic::getUserAuthInfo($this->user_id, $user_reg_type);
            
            if ($info) {
                return JsonServer::success('获取认证信息成功', $info);
            } else {
                return JsonServer::success('暂无认证信息', []);
            }
        } catch (\Exception $e) {
            return JsonServer::error('查询失败：' . $e->getMessage());
        }
    }


    /**
     * 资料提交汇付 开通
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendCheck(){
        $id = $this->request->get('id');
        $id = $id ?: 1;
        $info = UserAuthentication::where('id', '=', $id)->find();
//        $distribution = Distribution::where('user_id', '=', $info['user_id'])->find();
        $result =[];
        $huifuServer = HuifuPayServer::getInstance();
        if ($info['user_reg_type'] == 1){   //分销商
            if ($info['user_role_type'] == 1){  //个人
                $result = $huifuServer->reg_distribution_user($info);
            }
            if ($info['user_role_type'] == 2){  //企业
                $result = $huifuServer->reg_distribution_ent($info);
            }
        }
        return JsonServer::success('提交成功', $result);
    }

    /**
     * 更新银行卡 分账信息等 业务信息 提交汇付
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function sendUpdate(){
        $id = $this->request->get('id');
        $id = $id ?: 1;
        $info = UserAuthentication::where('id', '=', $id)->find();
//        $distribution = Distribution::where('user_id', '=', $info['user_id'])->find();
        $result =[];
        $huifuServer = HuifuPayServer::getInstance();
        //如果已经认证成功,调用更新
        if ($info['auth_status'] == 10){
            $result = $huifuServer->userBusiModify($info);
        }else{
            if ($info['user_reg_type'] == 1){   //分销商
                if ($info['user_role_type'] == 1){  //个人
                    $result = $huifuServer->userBusiOpen($info);
                }
                if ($info['user_role_type'] == 2){  //企业
                    $result = $huifuServer->userBusiOpen($info);
                }
            }
        }
        if(!$result){
            return JsonServer::error($huifuServer->getError());
        }
        return JsonServer::success('提交成功', $result);
    }

    public function userList()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userList($get);
        return JsonServer::success('成功', $result);
    }

    public function userQuery()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userQuery($get);
        return JsonServer::success('成功', $result);
    }


    /**
     * 用户余额查询
     */
    public function userBalanceQuery()
    {
        $get = $this->request->get();
        $get['is_total'] = $get['is_total'] ?? 0;
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userBalanceQuery($get, $get['is_total']);
        return JsonServer::success('成功', $result);
    }

    /**
     * 用户提现
     */
    public function userEncashment()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userEncashment($get);
        return JsonServer::success('成功', $result);
    }

    public function userEncashmentQuery()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userEncashmentQuery($get);
        return JsonServer::success('成功', $result);
    }

    public function userSettlementQuery()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userSettlementQuery($get);
        return JsonServer::success('成功', $result);
    }

    public function batchTranslogQuery()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->batchTranslogQuery($get);
        return JsonServer::success('成功', $result);
    }





    /**
     * 用户账户流水日志
     * @return \think\response\Json
     */
    public function userAcctlogQuery()
    {
        $get = $this->request->get();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->userAcctlogQuery($get);
        return JsonServer::success('成功', $result);
    }


    
    /**
     * 保存银行卡信息
     */
    public function dataSaveBank()
    {
        try {
            $post = $this->request->post();

            // 调用逻辑层处理保存和汇付接口调用
            $result = UserHuifuLogic::saveBankCardInfo($this->user_id, $post);
            
            if ($result['status']) {
                return JsonServer::success($result['message']);
            } else {
                return JsonServer::error($result['message']);
            }
        } catch (\Exception $e) {
            return JsonServer::error('操作失败：' . $e->getMessage());
        }
    }

    public function orderConfirm(){
        $id = $this->request->get('order_id');
        $id = $id ?: 1;
        $info = \app\common\model\order\Order::where('id', '=', $id)->find();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->orderConfirm($info);
        return JsonServer::success('提交成功', $result);
    }

    /**
     * 单个分销订单分账
     */
    public function distributionOrderConfirm()
    {
        $id = $this->request->get('distribution_order_id');
        $id = $id ?: 0;
        $model = new DistributionOrderGoods();
        $oneDistributionOrder = $model->alias('dog')
            ->join('order o', 'o.id = dog.order_id')
            ->where('dog.id', '=', $id)
            ->where('dog.status', '=', DistributionOrderGoodsEnum::STATUS_WAIT_HANDLE)
            ->field('dog.*, o.order_sn, o.transaction_id, o.pay_way, o.distribution_money_status, o.id as order_id, o.hf_seq_id')
            ->find();
        if (!$oneDistributionOrder){
            return JsonServer::error('订单不存在');
        }
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->distributionOrderConfirm($oneDistributionOrder);
        if ($result){
            return JsonServer::success('提交成功', $result);
        }else{
            return JsonServer::error($huifuServer->getError());
        }
    }

    public function orderQuery(){
        $id = $this->request->get('order_id');
        $id = $id ?: 1;
        $info = \app\common\model\order\Order::where('id', '=', $id)->find();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->orderQuery($info);
        return JsonServer::success('提交成功', $result);
    }

    /**
     * 交易分账明细查询
     */
    public function transSplitQuery()
    {
        $id = $this->request->get('order_id');
        $id = $id ?: 1;
        $info = \app\common\model\order\Order::where('id', '=', $id)->find();
        $huifuServer = HuifuPayServer::getInstance();
        $result = $huifuServer->transSplitQuery($info);
        return JsonServer::success('提交成功', $result);
    }

}
