<?php

namespace app\common\command;

use app\common\enum\DistributionEnum;
use app\common\enum\DistributionOrderGoodsEnum;
use app\common\enum\PayEnum;
use app\common\logic\OrderCommonLogic;
use app\common\model\AccountLog;
use app\common\model\after_sale\AfterSale;
use app\common\model\distribution\Distribution;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\order\Order;
use app\common\model\user\User;
use app\common\server\ConfigServer;
use app\common\server\HuifuPayServer;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use think\facade\Log;

class HuifuDistributionOrderSharing extends Command
{
    protected function configure()
    {
        $this->setName('huifu_distribution_order_sharing')
            ->setDescription('汇付支付方式支付的订单分佣/交易确认(一次一条,只执行分销订单, 平台服务费/商户货款通过其他方式分账结算)');
    }

    /**
     * // 功能逻辑要点:
     * // 1、获取结算时间
     * // 2、查询可以结算的销售订单Order 列表 , 排除已完整结算的订单, 已经全部退款的订单;
     * // 3. 根据销售订单查询分销订单DistributionOrderGoods , 分销订单可能被多次结算, 订单可能被多次售后, 过滤掉分销用户没有认证的分销订单, 要注意过滤
     * // 4. 请求汇付分账逻辑方法HuifuPayServer的 orderConfirm() 接口去结算分销订单 ,
     * // 5. 要根据ls_order.distribution_money_status ((汇付支付)佣金分账状态:0未处理;10部分分账;20完全完成分账;99分账出错) 判定如何调用汇付分账方法;
     * //  0未处理, 可以传入整个订单去分账;
     * //  10部分分账, 传入分销订单中未分账的子订单去分账;
     * //  20完全完成分账, 不需要分账;
     * //  99分账出错, 不需要分账;
     * 
     * //  根据结果处理, 如果分销订单结算失败, 记录失败原因
     * //  如果结算成功, 更新分销订单状态,
     * //  更新销售订单的distribution_money等; 如果整个销售订单关联的分销订单都完成结算, 则更改销售订单状态;
     * //  更新分销会员的佣金,记录日志
     * 汇付交易确认接口 https://paas.huifu.com/open/doc/api/#/smzf/api_jyqr
     * 
     * @param Input $input
     * @param Output $output
     * @return bool
     */
    protected function execute(Input $input, Output $output)
    {
        echo 'start--HuifuDistributionOrderSharing-'.PHP_EOL;
        
        // 检查是否启用自动确认
        $is_auto_huifu_order_sharing = config('pay.is_auto_huifu_order_sharing');
        if ($is_auto_huifu_order_sharing != 1) {
            echo '未启用自动确认is_auto_huifu_order_sharing';
            return false;
        }

        //两种情况:
        // 一种情况 整个订单都没有结算过, 整单结算/不分结算
        // 第二种情况: 订单部分结算过, 结算剩余部分,如果全都结算完,则更改整单状态为结算完成
        $time = time();
        try {
            $result = OrderCommonLogic::getWaitSettleDistributionOrderList();
            // 确保$result是数组
            if (!$result || !is_array($result)) {
                echo '没有待结算的订单或失效订单';
                return false;
            }
            $invalidDistributionOrderLists = $result['invalid'] ?? [];
            $validDistributionOrders = $result['valid'] ?? [];
            
            if (empty($invalidDistributionOrderLists) && empty($validDistributionOrders)){
                echo '没有待结算的订单或失效订单';
                return false;
            }
            
            // 批量更新失效的分销订单
            if (!empty($invalidDistributionOrderLists)) {
                DistributionOrderGoods::update([
                    'status' => DistributionOrderGoodsEnum::STATUS_ERROR,
                    'update_time' => $time
                ], [['id', 'in', $invalidDistributionOrderLists]]);
            }
            if (!$validDistributionOrders) {
                echo '没有待结算的订单';
                return false;
            }
            //查询分销商表,检测分销商是否已经开通汇付账号
            $distributionUserIds = array_values(array_unique(array_column($validDistributionOrders, 'user_id')));
            $distributionUserCodes = Distribution::where('user_id', 'in', $distributionUserIds)->column('pay_partner_account_code', 'user_id');
            // 处理每个有效订单
            foreach ($validDistributionOrders as $distributionOrder) {
                $error = '';
                //如果没有汇付商户号, 跳过
                $d_huifu_id = $distributionUserCodes[$distributionOrder['user_id']]  ?? '';
                if (!$d_huifu_id) {
                    $error = "分销订单{$distributionOrder['id']} 用户 {$distributionOrder['user_id']}没有对应的商户号, 暂不结算";
                }
                if (!$distributionOrder['hf_seq_id']){
                    $error = "分销订单{$distributionOrder['id']}  未获取汇付流水号";
                }
                if ($error){
                    DistributionOrderGoods::where('id', $distributionOrder['id'])->update(['status_update_error' => $error]);
                    echo $error;
                    continue;
                }

                $huifuServer = HuifuPayServer::getInstance();
                // 格式化单个分销订单为分账信息
                $acct_split_bunch = [
                    'percentage_flag'=>'N',
                    'total_div_amt'=>$distributionOrder['money'],
                    'acct_infos'=>[
                        [
                            'huifu_id'=>$d_huifu_id,
                            'div_amt'=>$distributionOrder['money'],
                        ]
                    ]
                ];
                // 查询已经分账的分账信息, 防止重复分账!!!
                $huifu_finished_split_order = $huifuServer->transSplitQuery($distributionOrder);
                if ($huifu_finished_split_order){
                    foreach ($huifu_finished_split_order as $item){
                        // 判断是否已经分账, 账号相同, 金额相等 则报错
                        if ($item['in_huifu_id'] == $d_huifu_id && (abs($item['split_amt'] - $distributionOrder['money']) < 0.01)){
                            $error = $item['in_huifu_id'].'已分账'.$item['split_amt'].'元';
                            DistributionOrderGoods::where('id', $distributionOrder['id'])->update(['status_update_error' => $error]);
                            echo $error;
                            break;
                        }
                    }
                }
                // 4、根据订单分账状态调用汇付分账接口
                Db::startTrans();
                try {
                    $result = $huifuServer->distributionOrderConfirm($distributionOrder, $acct_split_bunch);
                    if (!$result) {
                        $error = $huifuServer->getError();
                        throw new \Exception($error ?? '未知错误');
                    }
                    Db::commit();
                    echo 'Success processing id:' . $distributionOrder['id'] . PHP_EOL;
                } catch (\Exception $e) {
                    Db::rollback();
                    $error = '分账失败: id:'.$distributionOrder['id']. ' - 订单id'. $distributionOrder['order_id'] . ' - ' . $e->getMessage() . PHP_EOL;
                    Log::write($error);
                    Log::write($distributionOrder);
                    DistributionOrderGoods::where('id', $distributionOrder['id'])->update(['status_update_error' => $error]);
                    echo $error;
                }
                // 避免请求过于频繁
                sleep(2);
            }
            return true;
        } catch (\Exception $e) {
            Log::write('汇付分销结算异常:' . $e->getMessage());
            return false;
        }
    }
}
