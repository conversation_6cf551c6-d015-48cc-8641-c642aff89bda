<?php

namespace app\api\controller;

use app\admin\logic\finance\WithdrawLogic;
use app\api\logic\PayLogic;
use app\common\basics\Api;
use app\common\enum\OrderEnum;
use app\common\enum\PayEnum;
use app\common\logic\OrderRefundLogic;
use app\common\model\Client_;
use app\common\model\order\OrderTrade;
use app\common\model\order\Order;
use app\common\model\order\OrderLog;
use app\common\server\AliPayServer;
use app\common\server\DouGong\BaseAsync;
use app\common\server\DouGong\pay\PayZhengSao;
use app\common\server\JsonServer;
use app\common\model\Test;
use app\common\server\WeChatPayServer;
use app\common\server\WeChatServer;
use app\common\model\integral\IntegralOrder;
use think\facade\Log;

/**
 * Class Pay
 * @package app\api\controller
 */
class Pay extends Api
{
    public $like_not_need_login = ['notifyMnp', 'notifyOa', 'notifyApp', 'aliNotify', 'hfdgPayWechatNotify', 'hfdgPayAlipayNotify', 'hfdgRefundWechatNotify'];

    /**
     * @notes 支付入口
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/7/13 6:13 下午
     */
    public function unifiedpay()
    {
        $post = $this->request->post();
        if(!isset($post['pay_way'])) {
            return JsonServer::error('请选择支付方式');
        }
        if ($post['from'] == 'order') {
            // 更新支付方式
            $order = Order::findOrEmpty($post['order_id']);
            $order->pay_way = $post['pay_way'];
            $order->save();
            $pay_way = $order['pay_way'];
        }
        if ($post['from'] == 'trade') {
            $order = Order::where('trade_id', $post['order_id'])->findOrEmpty();
            // 更新支付方式
            Order::where('trade_id', $post['order_id'])->update(['pay_way' => $post['pay_way']]);
            $pay_way = $post['pay_way'];
        }
        if ($post['from'] == 'recharge') {
            $pay_way = isset($post['pay_way']) ? $post['pay_way'] : PayEnum::WECHAT_PAY;
        }

        // 积分订单
        if ($post['from'] == 'integral') {
            $order = IntegralOrder::findOrEmpty($post['order_id']);
            IntegralOrder::where('id', $post['order_id'])->update(['pay_way' => $post['pay_way']]);
            $pay_way = $post['pay_way'];
        }

        // order,trade方式金额为0直接走余额支付
        if (isset($order) && $order['order_amount'] == 0) {
            $result = PayLogic::balancePay($post['order_id'], $post['from']);
            return $result;
        }
        switch ($pay_way) {
            case OrderEnum::PAY_WAY_BALANCE://余额支付
                $result = PayLogic::balancePay($post['order_id'], $post['from']);
                break;
            case OrderEnum::PAY_WAY_WECHAT://微信支付
                $result = PayLogic::wechatPay($post['order_id'], $post['from'], $this->client);
                break;
            case OrderEnum::PAY_WAY_ALIPAY://支付宝支付
                $result = PayLogic::aliPay($post['order_id'], $post['from'],$this->client);
                $data = [
                    'code' => 10001,
                    'msg' => '发起成功',
                    'data' => $result,
                    'show' => 0,
                ];
                return json($data);
            case OrderEnum::HFDG_WECHAT://汇付微信支付
                return PayLogic::huifuPay($post['order_id'], $post['from'], $this->client, $pay_way);
        }

        return $result;

    }



    /**
     * @notes 小程序回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:13 下午
     */
    public function notifyMnp()
    {

        $config = WeChatServer::getPayConfig(Client_::mnp);
        return WeChatPayServer::notify($config);
    }


    /**
     * @notes 公众号回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:13 下午
     */
    public function notifyOa()
    {

        $config = WeChatServer::getPayConfig(Client_::oa);
        return WeChatPayServer::notify($config);
    }


    /**
     * @notes APP回调
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:14 下午
     */
    public function notifyApp()
    {

        $config = WeChatServer::getPayConfig(Client_::ios);
        return WeChatPayServer::notify($config);
    }



    /**
     * @notes 支付宝回调
     * @return bool
     * <AUTHOR>
     * @date 2021/7/13 6:14 下午
     */
    public function aliNotify()
    {
        $data = $this->request->post();
        $result = (new AliPayServer())->verifyNotify($data);
        if (true === $result) {
            echo 'success';
        } else {
            echo 'fail';
        }
    }


    /**
     * 使用原方式验证回调
     * @notes 汇付微信回调
     * @return string
     */
    function hfdgPayWechatNotify()
    {
        $data = input();
        Log::write('hfdgPayWechatNotify--------');
        Log::write($data);
        $async = new BaseAsync($data);
        $async->checkAsync();
        if ($async->getCheckSuccess()) {
            PayZhengSao::asyncSuccessDeal($data);
        }
        return $async->getCheckSuccess() ? 'success' : 'failed';
    }

    function hfdgPayAlipayNotify()
    {
        $data = input();

        $async = new BaseAsync($data);

        $async->checkAsync();

        if ($async->getCheckSuccess()) {
            PayZhengSao::asyncSuccessDeal($data);
        }

        return $async->getCheckSuccess() ? 'success' : 'failed';
    }

    /**
     * 使用原方式验证回调
     * @notes 汇付微信回调
     * @return string
     */
    function hfdgRefundWechatNotify()
    {
        $data = input();
        Log::write('hfdgRefundWechatNotify--------');
        Log::write($data);

        $async = new BaseAsync($data);
        $async->checkAsync();
        if ($async->getCheckSuccess()) {
            OrderRefundLogic::huifuPayRefundDone($data);
        }else{
            OrderRefundLogic::huifuPayRefundDone($data, $async->getMsg());
        }
        return $async->getCheckSuccess() ? 'success' : 'failed';
    }

    /**
     * 汇付主动取现回调
     * https://paas.huifu.com/open/doc/api/#/jyjs/qx/api_qx
     * @return string
     */
    function hfdgWithdrawNotify()
    {
        $data = input();
        Log::write('hfdgWithdrawNotify--------');
        Log::write($data);
        $async = new BaseAsync($data);
        $async->checkAsync();
        if ($async->getCheckSuccess()) {
            $resp_data = $data['resp_data'] ?? [];
            $resp_data = json_decode($resp_data, true);
            \app\api\logic\WithdrawLogic::withdrawDoneNotify($resp_data);
        }
        return $async->getCheckSuccess() ? 'success' : 'failed';
    }
}