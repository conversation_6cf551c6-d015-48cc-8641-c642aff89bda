<?php
// +----------------------------------------------------------------------
// | likeshop开源商城系统
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | gitee下载：https://gitee.com/likeshop_gitee
// | github下载：https://github.com/likeshop-github
// | 访问官网：https://www.likeshop.cn
// | 访问社区：https://home.likeshop.cn
// | 访问手册：http://doc.likeshop.cn
// | 微信公众号：likeshop技术社区
// | likeshop系列产品在gitee、github等公开渠道开源版本可免费商用，未经许可不能去除前后端官方版权标识
// |  likeshop系列产品收费版本务必购买商业授权，购买去版权授权后，方可去除前后端官方版权标识
// | 禁止对系统程序代码以任何目的，任何形式的再发布
// | likeshop团队版权所有并拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeshop.cn.team
// +----------------------------------------------------------------------

namespace app\common\enum;

/**
 * 支付
 * Class PayEnum
 * @package app\common\enum
 */
class PayEnum
{
    /**
     * 支付方式
     */
    const WECHAT_PAY    = 1; //微信支付
    const ALI_PAY       = 2; //支付宝支付
    const BALANCE_PAY   = 3; //余额支付
    const OFFLINE_PAY   = 4; //线下支付

    // 汇付斗拱微信
    const HFDG_WECHAT = 55;
    // 汇付斗拱支付宝
    const HFDG_ALIPAY = 66;
    

    const UNPAID = 0;//待支付
    const ISPAID = 1;//已支付
    const REFUNDED = 2;//已退款
    const REFUSED_REFUND = 3;//拒绝退款

    const PAY_WAY_KIND_NORMAL = 1;
    const PAY_WAY_KIND_HUIFU = 2;

    public static function getPayWayByKind($pay_way_kind_huifu){
        if($pay_way_kind_huifu == PayEnum::PAY_WAY_KIND_NORMAL){
            return [
                self::WECHAT_PAY,
                self::ALI_PAY,
                self::BALANCE_PAY,
                self::OFFLINE_PAY
            ];
        }else{
            return [
                self::HFDG_WECHAT,
                self::HFDG_ALIPAY
            ];
        }
    }
    

    /**
     * Notes: 支付方式
     * @param bool $type
     * <AUTHOR> 15:01)
     * @return array|mixed|string
     */
    public static function getPayWay($type = true)
    {
        $data = [
            self::HFDG_WECHAT    => '微信支付(hf)',
            self::HFDG_ALIPAY    => '支付宝支付(hf)',
            self::WECHAT_PAY    => '微信支付',
            self::ALI_PAY       => '支付宝支付',
            self::BALANCE_PAY   => '余额支付',
            self::OFFLINE_PAY   => '线下支付',
        ];
        if (true === $type) {
            return $data;
        }
        return $data[$type] ?? '-';
    }


    //支付状态
    public static function getPayStatus($type)
    {
        $data = [
            PayEnum::UNPAID => '待支付',
            PayEnum::ISPAID => '已支付',
            PayEnum::REFUNDED => '已退款',
            PayEnum::REFUSED_REFUND => '拒绝退款',
        ];

        if ($type === true) {
            return $data;
        }
        return $data[$type] ?? '未知';
    }
}