<?php

namespace app\api\controller;

use app\common\basics\Api;
use app\api\validate\WithdrawValidate;
use app\common\model\user\UserPay;
use app\common\server\ConfigServer;
use app\common\server\JsonServer;
use app\api\logic\WithdrawLogic;
use app\common\server\UrlServer;

/**
 * Class Withdraw
 * @package app\api\controller
 */
class Withdraw extends Api
{

    /**
     * @notes 提现申请
     * @return \think\response\Json
     * @throws \think\Exception
     * <AUTHOR>
     * @date 2021/7/13 6:16 下午
     */
    public function apply()
    {
        $post = $this->request->post();
        $post['user_id'] = $this->user_id;
        (new WithdrawValidate())->goCheck('apply', $post);
        $month_day = date('j', time());
        $withdraw_start = ConfigServer::get('withdraw', 'withdraw_start', 15);
        $withdraw_end = ConfigServer::get('withdraw', 'withdraw_end', 15);
        if ($this->user_id < 9){
            $withdraw_start = $withdraw_start -1;
        }
        if($withdraw_start <= $withdraw_end){
            $is_able_withdrawal = $month_day >= $withdraw_start && $month_day <= $withdraw_end ? 1 : 0;
        }else{
            $is_able_withdrawal = $month_day <= $withdraw_end || $month_day >= $withdraw_start ? 1 : 0;
        }
        if (!$is_able_withdrawal){
            return JsonServer::error('当前日期不允许提现');
        }
        $id = WithdrawLogic::apply($post);
        return JsonServer::success('申请成功', ['id' => $id]);
    }

    /**
     * @notes 提现配置
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:16 下午
     */
    public function config()
    {

        $data = WithdrawLogic::config($this->user_id);
        return JsonServer::success('', $data);
    }

    /**
     * @notes 提现记录
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:16 下午
     */
    public function records()
    {

        $get = $this->request->get();
        $page = $this->request->get('page_no', $this->page_no);
        $size = $this->request->get('page_size', $this->page_size);
        $res = WithdrawLogic::records($this->user_id, $get, $page, $size);
        return JsonServer::success('', $res);
    }

    /**
     * @notes 提现详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/7/13 6:16 下午
     */
    public function info()
    {

        $get = $this->request->get('');
        (new WithdrawValidate())->goCheck('info', $get);
        $res = WithdrawLogic::info($get['id'], $this->user_id);
        return JsonServer::success('', $res);
    }

    /**
     * Notes: 获取上次体现方式基本信息
     * Author: Darren
     * DateTime: 2023-07-07 9:56
     */
    public function getlastmethod(){
        $where['type'] = 3;
        $where['user_id'] = $this->user_id;
        $field = 'real_name,account,type,money_qr_code,bank,subbank,id_front,id_back,id_number,mobile';
        $info[3] = \app\common\model\WithdrawApply::field($field)->where($where)->order('id desc')->find();
        $where['type'] = 4;
        $info[4] = \app\common\model\WithdrawApply::field($field)->where($where)->order('id desc')->find();
        $where['type'] = 5;
        $info[5] = \app\common\model\WithdrawApply::field($field)->where($where)->order('id desc')->find();
        foreach ($info as $k=>$value){
            if (!$value){
                unset($info[$k]);
                continue;
            }else{
                if (isset($value['money_qr_code']) && $value['money_qr_code']){
                    $info[$k]['money_qr_code'] = UrlServer::getFileUrl($value['money_qr_code']);
                }
                if (isset($value['id_front']) && $value['id_front']){
                    $info[$k]['id_front'] = UrlServer::getFileUrl($value['id_front']);
                }
                if (isset($value['id_back']) && $value['id_back']){
                    $info[$k]['id_back'] = UrlServer::getFileUrl($value['id_back']);
                }
                $info[$k]=$info[$k]->toArray();
            }
        }
        $info = $info ? array_values($info) : [];
        return JsonServer::success('', $info);
    }

    /**
     * Notes: 获取默认收款账号
     * Author: Darren
     * DateTime:
     */
    public function getuserpay(){
        $id = $this->request->get('id');
        $where[] = ['user_id', '=', $this->user_id];
        if ($id){
            $where[] = ['id', '=', $id];
        }else{
            $where[] = ['is_default', '=', 1];
        }
        $withdraw_type = ConfigServer::get('withdraw', 'type');
        if ($withdraw_type){
            $where[] = ['account_type', '=', $withdraw_type];
        }
        $info = UserPay::where($where)->order('update_time', 'desc')->find();
        if ($info){
            $info = $info->toArray();
            $info['money_qr_code'] = isset($info['money_qr_code']) && $info['money_qr_code'] ? UrlServer::getFileUrl($info['money_qr_code']) : '';
        }else{
            $info =[];
        }
        return JsonServer::success('', $info);
    }

    /**
     * Notes: 获取默认收款账号
     * Author: Darren
     * DateTime:
     */
    public function setuserpay(){
        $id = $this->request->post('id');
        $data['account'] = $this->request->post('account');
        $data['account_type'] = ConfigServer::get('withdraw', 'type');//当前可用的提现方式
        $data['is_default'] = $this->request->post('is_default') ?: 0;
        $data['real_name'] = $this->request->post('real_name');
        $data['remark'] = $this->request->post('remark');
        $money_qr_code = $this->request->post('money_qr_code');
        if ($money_qr_code){
            $data['money_qr_code'] = $money_qr_code;
        }
        $data['user_id'] = $this->user_id;
        if (!$data['account'] || !$data['real_name'] ){
            return JsonServer::error('提交信息不完整');
        }
        if ($id){
            $where['id'] = $id;
            $where['user_id'] = $this->user_id;
            $info = UserPay::where($where)->find();
            if (!$info){
                return JsonServer::error('未获取账号信息');
            }
            UserPay::update($data,  ['id'=>$info['id']]);
        }else{
            UserPay::create($data);
        }
        return JsonServer::success('', $data);
    }
}